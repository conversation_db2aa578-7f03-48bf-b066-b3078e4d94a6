import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  Tooltip,
  Avatar,
  ActionMenu,
  ActionMenuItem,
} from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import {
  User,
  UserStatus,
  UserType,
  UserGender,
  UserQueryDto,
  BlockUserDto,
  UnblockUserDto,
} from '../types/user.types';
import UserForm from '../components/forms/UserForm';
import {
  useUsers,
  useUpdateUser,
  useDeleteUser,
  useBlockUser,
  useUnblockUser,
} from '../hooks/useUserQuery';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { ConfirmDeleteModal } from '@/modules/admin/marketplace/components/modals';

/**
 * Trang quản lý danh sách người dùng (phiên bản tối ưu)
 */
const UserListPageOptimized: React.FC = () => {
  const { t } = useTranslation(['user', 'common']);

  // State cho người dùng đang chỉnh sửa
  const [editingUser, setEditingUser] = useState<User | undefined>(undefined);

  // State cho dialog xác nhận xóa
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Xử lý chỉnh sửa
  const handleEditUser = useCallback(
    (user: User) => {
      setEditingUser(user);
      showForm();
    },
    [showForm]
  );

  // Xử lý toggle block/unblock user
  const handleToggleUserStatus = useCallback(
    (user: User) => {
      const isCurrentlyBlocked = user.status === UserStatus.SUSPENDED;

      if (isCurrentlyBlocked) {
        // Unblock user
        const unblockData: UnblockUserDto = {
          reason: t('user:actions.unblockReason', 'User status restored by admin'),
          info: {
            details: t('user:actions.unblockDetails', 'User has been unblocked'),
            approvedBy: '<EMAIL>',
          },
        };

        unblockUser.mutate(
          { id: user.id, data: unblockData },
          {
            onSuccess: () => {
              refetch();
            },
          }
        );
      } else {
        // Block user
        const blockData: BlockUserDto = {
          reason: t('user:actions.blockReason', 'User blocked by admin'),
          info: {
            details: t('user:actions.blockDetails', 'User has been blocked'),
            reportedBy: '<EMAIL>',
          },
        };

        blockUser.mutate(
          { id: user.id, data: blockData },
          {
            onSuccess: () => {
              refetch();
            },
          }
        );
      }
    },
    [ t]
  );

  // Columns cho bảng
  const columns = useMemo<TableColumn<User>[]>(
    () => [
      {
        key: 'id',
        title: t('user:fields.id', 'ID'),
        dataIndex: 'id',
        width: '5%',
        sortable: true,
      },
      {
        key: 'fullName',
        title: t('user:fields.fullName', 'Họ và tên'),
        dataIndex: 'fullName',
        width: '20%',
        sortable: true,
        render: (value: unknown, record: User) => (
          <div className="flex items-center space-x-3">
            <Avatar
              {...(record.avatarUrl && { src: record.avatarUrl })}
              alt={record.fullName}
              size="sm"
            />
            <div>
              <div className="font-medium">{value as string}</div>
              <div className="text-xs text-gray-500">{record.email}</div>
            </div>
          </div>
        ),
      },
      {
        key: 'phoneNumber',
        title: t('user:fields.phoneNumber', 'Số điện thoại'),
        dataIndex: 'phoneNumber',
        width: '10%',
        sortable: true,
      },
      {
        key: 'status',
        title: t('user:fields.status', 'Trạng thái'),
        dataIndex: 'status',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const status = value as UserStatus;
          return (
            <div
              className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
                status === UserStatus.ACTIVE
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : status === UserStatus.SUSPENDED
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    : status === UserStatus.INACTIVE
                      ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
              }`}
            >
              {status === UserStatus.ACTIVE
                ? t('user:status.active', 'Hoạt động')
                : status === UserStatus.SUSPENDED
                  ? t('user:status.suspended', 'Tạm khóa')
                  : status === UserStatus.INACTIVE
                    ? t('user:status.inactive', 'Không hoạt động')
                    : t('user:status.deleted', 'Đã xóa')}
            </div>
          );
        },
      },
      {
        key: 'type',
        title: t('user:fields.accountType', 'Loại tài khoản'),
        dataIndex: 'type',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const type = value as UserType;
          return (
            <div
              className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
                type === UserType.INDIVIDUAL
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                  : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
              }`}
            >
              {type === UserType.INDIVIDUAL
                ? t('user:accountType.individual', 'Cá nhân')
                : t('user:accountType.business', 'Doanh nghiệp')}
            </div>
          );
        },
      },
      {
        key: 'verification',
        title: t('user:fields.verification', 'Xác thực'),
        width: '10%',
        render: (_: unknown, record: User) => (
          <div className="flex space-x-2">
            {record.isVerifyEmail && (
              <Tooltip content={t('user:verification.email', 'Email')} position="top">
                <div className="px-2 py-1 rounded-full text-center text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                  {t('user:verification.email', 'Email')}
                </div>
              </Tooltip>
            )}
            {record.isVerifyPhone && (
              <Tooltip content={t('user:verification.phone', 'Điện thoại')} position="top">
                <div className="px-2 py-1 rounded-full text-center text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                  {t('user:verification.phone', 'Điện thoại')}
                </div>
              </Tooltip>
            )}
            {!record.isVerifyEmail && !record.isVerifyPhone && (
              <div className="px-2 py-1 rounded-full text-center text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                {t('user:verification.notVerified', 'Chưa xác thực')}
              </div>
            )}
          </div>
        ),
      },

      {
        key: 'actions',
        title: t('user:fields.actions', 'Thao tác'),
        width: '10%',
        render: (_: unknown, record: User) => {
          const isBlocked = record.status === UserStatus.SUSPENDED;

          const actionItems: ActionMenuItem[] = [
            {
              id: 'edit',
              label: t('user:actions.edit', 'Chỉnh sửa'),
              icon: 'edit',
              onClick: () => handleEditUser(record),
              showDirect: true,
              tooltip: t('user:actions.edit', 'Chỉnh sửa'),
            },
            {
              id: 'toggle-status',
              label: isBlocked ? t('common:unblock', 'Unblock') : t('common:block', 'Block'),
              icon: isBlocked ? 'toggle-off' : 'toggle-on',
              onClick: () => handleToggleUserStatus(record),
              tooltip: isBlocked
                ? t('common:unblock', 'Unblock User')
                : t('common:block', 'Block User'),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('user:actions.moreActions', 'Thêm hành động')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="160px"
              preferRight={true}
              preferTop={true}
            />
          );
        },
      },
    ],
    [t, handleEditUser, handleToggleUserStatus]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      {
        id: 'active',
        label: t('user:status.active', 'Hoạt động'),
        icon: 'check',
        value: UserStatus.ACTIVE,
      },
      {
        id: 'inactive',
        label: t('user:status.inactive', 'Không hoạt động'),
        icon: 'x',
        value: UserStatus.INACTIVE,
      },
      {
        id: 'suspended',
        label: t('user:status.suspended', 'Tạm khóa'),
        icon: 'alert-triangle',
        value: UserStatus.SUSPENDED,
      },
      {
        id: 'deleted',
        label: t('user:status.deleted', 'Đã xóa'),
        icon: 'trash',
        value: UserStatus.DELETED,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): UserQueryDto => {
    const queryParams: UserQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    // Thêm filter theo trạng thái nếu không phải 'all'
    if (params.filterValue !== 'all') {
      queryParams.status = params.filterValue as UserStatus;
    }

    // Thêm filter theo khoảng thời gian nếu có
    if (params.dateRange[0]) {
      queryParams.startDate = params.dateRange[0].toISOString();
    }
    if (params.dateRange[1]) {
      queryParams.endDate = params.dateRange[1].toISOString();
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<User, UserQueryDto>({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );

  // Lấy danh sách người dùng
  const { data: userData, isLoading, refetch } = useUsers(dataTable.queryParams);

  // Mutations
  const { mutate: updateUser, isPending: isUpdating } = useUpdateUser();
  const { mutate: deleteUser, isPending: isDeleting } = useDeleteUser();
  const { mutate: blockUser, isPending: isBlocking } = useBlockUser();
  const { mutate: unblockUser, isPending: isUnblocking } = useUnblockUser();

  // Xác nhận xóa
  const handleConfirmDelete = () => {
    if (userToDelete) {
      deleteUser(userToDelete.id, {
        onSuccess: () => {
          setShowDeleteConfirm(false);
          setUserToDelete(null);
          refetch();
        },
      });
    }
  };

  // Hủy xóa
  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
    setUserToDelete(null);
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    if (editingUser) {
      // Cập nhật người dùng
      updateUser(
        {
          id: editingUser.id,
          data: {
            fullName: values['fullName'] as string,
            email: values['email'] as string,
            phoneNumber: values['phoneNumber'] as string,
            type: values['type'] as UserType,
            gender: values['gender'] as UserGender,
            dateOfBirth: values['dateOfBirth'] as string,
            address: values['address'] as string,
          },
        },
        {
          onSuccess: () => {
            hideForm();
            refetch();
          },
        }
      );
    }
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
    setEditingUser(undefined);
  };

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [UserStatus.ACTIVE]: t('user:status.active', 'Hoạt động'),
      [UserStatus.INACTIVE]: t('user:status.inactive', 'Không hoạt động'),
      [UserStatus.SUSPENDED]: t('user:status.suspended', 'Tạm khóa'),
      [UserStatus.DELETED]: t('user:status.deleted', 'Đã xóa'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      <SlideInForm isVisible={isVisible}>
        {editingUser && (
          <UserForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            userData={editingUser}
            isSubmitting={isUpdating}
          />
        )}
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={userData?.items || []}
          rowKey="id"
          loading={isLoading || isDeleting || isBlocking || isUnblocking}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: userData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: userData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('user:deleteUserConfirmTitle', 'Xác nhận xóa người dùng')}
        message={t('user:deleteUserConfirmMessage', {
          name: userToDelete?.fullName || '',
        })}
        isSubmitting={isDeleting}
      />
    </div>
  );
};

export default UserListPageOptimized;
